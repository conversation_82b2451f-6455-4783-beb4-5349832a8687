<template>
  <el-dialog
    title="添加智能体"
    v-model="dialogVisible"
    :width="1200"
    @close="handleClose"
  >
    <div class="agent-selection">
      <el-button style="margin-bottom: 20px" type="primary" @click="createAgent"
        >创建智能体</el-button
      >

      <!-- 使用 el-table 组件 -->
      <el-table
        ref="selectionTable"
        :data="allAgentList"
        class="agent-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column label="智能体名称" width="180">
          <template #default="scope">
            <div class="agent-name-cell">
              <el-image class="elImage" :src="scope.row.avatar">
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <span class="agent-name">{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="intro" label="简介" />
      </el-table>

      <div class="pagination">
        <span>已选/{{ selectedCount }}</span>
        <el-pagination
          layout="pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { Picture } from '@element-plus/icons-vue'
export default {
  components: {
    Picture,
  },
  name: 'AgentSelectionModal',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    total: {
      type: Number,
      default: 24,
    },
  },
  data() {
    return {
      formShow: false,
      dialogVisible: this.visible,
      pageSize: 8,
      currentPage: 1,
      selectedAgents: [],
      allAgentList: [
        {
          name: '智能导游A',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-19/upload_52325c1a2757a56b1a54cfd0db658649.jpg',
          intro:
            '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。我内置全国 5A/4A 景区厘米级三维地图与实时人流热力图，能在 0.1 秒内为你规划最优游览动线，避开排队高峰；支持 28 种语言同声讲解，走到哪讲到哪，还能切换童声、评书、脱口秀等 12 种讲解风格；AR 眼镜或手机摄像头一对，古迹立刻"复原"，宋代…',
        },
        {
          name: '智能导游B',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-19/upload_52325c1a2757a56b1a54cfd0db658649.jpg',
          intro:
            '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。我内置全国 5A/4A 景区厘米级三维地图与实时人流热力图，能在 0.1 秒内为你规划最优游览动线，避开排队高峰；支持 28 种语言同声讲解，走到哪讲到哪，还能切换童声、评书、脱口秀等 12 种讲解风格；AR 眼镜或手机摄像头一对，古迹立刻"复原"，宋代…',
        },
        {
          name: '智能导游C',
          avatar:
            'https://prod.shukeyun.com/maintenance/deepfile/data/2025-08-15/upload_1e3fcd92856e16adc98e068f742d2b8d.png',
          intro:
            '大家好，我是"云游小导"——专为景区设计的 AI 导游虚拟导游。我内置全国 5A/4A 景区厘米级三维地图与实时人流热力图，能在 0.1 秒内为你规划最优游览动线，避开排队高峰；支持 28 种语言同声讲解，走到哪讲到哪，还能切换童声、评书、脱口秀等 12 种讲解风格；AR 眼镜或手机摄像头一对，古迹立刻"复原"，宋代…',
        },
      ],
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      this.resetSelection()
    },
  },
  computed: {
    selectedCount() {
      return this.selectedAgents.length
    },
  },
  methods: {
    handleSelectionChange(selection) {
      this.selectedAgents = selection
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.resetSelection()
    },
    resetSelection() {
      this.selectedAgents = []
      if (this.$refs.selectionTable) {
        this.$refs.selectionTable.clearSelection()
      }
    },
    confirmSelection() {
      // 修复：先触发事件，再关闭弹窗和重置选择
      this.$emit('confirm', [...this.selectedAgents]) // 使用浅拷贝确保数据不变
      this.dialogVisible = false
      this.resetSelection()
    },
    createAgent() {
      this.formShow = true
    },
    handleSizeChange(size) {
      this.pageSize = size
    },
    handleCurrentChange(page) {
      this.currentPage = page
    },
  },
}
</script>

<style lang="less" scoped>
.agent-selection {
  padding: 20px;

  .agent-table {
    width: 100%;
    margin-bottom: 15px;

    :deep(.el-table__header-wrapper) {
      th {
        background-color: #f5f7fa;
        font-weight: bold;
      }
    }

    :deep(.el-table__row) {
      &:hover {
        background-color: #f9f9f9;
      }
    }

    .agent-name-cell {
      display: flex;
      align-items: flex-start;
      padding: 8px 0;
      .elImage {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        margin-right: 6px;
      }

      .agent-name {
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
